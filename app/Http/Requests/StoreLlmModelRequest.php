<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreLlmModelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add proper authorization logic if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:llm_models,name',
            'display_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'huggingface_id' => 'nullable|string|max:255',
            'huggingface_url' => 'nullable|url',
            
            // Capabilities
            'has_reasoning' => 'boolean',
            'has_tool_usage' => 'boolean',
            'has_vision' => 'boolean',
            'has_code_generation' => 'boolean',
            'has_function_calling' => 'boolean',
            
            // Technical specifications
            'context_window' => 'nullable|integer|min:1',
            'parameter_count' => 'nullable|string|max:50',
            'architecture' => 'nullable|string|max:100',
            'quantization' => 'nullable|string|max:50',
            'format' => 'nullable|string|max:50',
            
            // Chat and prompt formatting
            'chat_template' => 'nullable|string|max:255',
            'prompt_templates' => 'nullable|array',
            
            // Performance and resource requirements
            'model_size_bytes' => 'nullable|integer|min:0',
            'estimated_vram_gb' => 'nullable|integer|min:0',
            'benchmark_score' => 'nullable|numeric|min:0|max:999999.99',
            'benchmark_details' => 'nullable|array',
            
            // Licensing and usage
            'license' => 'nullable|string|max:100',
            'commercial_use' => 'boolean',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:100',
            
            // Status
            'status' => 'nullable|in:active,inactive,deprecated',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The model name is required.',
            'name.unique' => 'A model with this name already exists.',
            'context_window.integer' => 'Context window must be a valid number.',
            'context_window.min' => 'Context window must be at least 1.',
            'model_size_bytes.integer' => 'Model size must be a valid number.',
            'estimated_vram_gb.integer' => 'Estimated VRAM must be a valid number.',
            'benchmark_score.numeric' => 'Benchmark score must be a valid number.',
            'benchmark_score.max' => 'Benchmark score cannot exceed 999999.99.',
            'status.in' => 'Status must be one of: active, inactive, deprecated.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string booleans to actual booleans
        $booleanFields = [
            'has_reasoning',
            'has_tool_usage', 
            'has_vision',
            'has_code_generation',
            'has_function_calling',
            'commercial_use'
        ];

        foreach ($booleanFields as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => filter_var($this->input($field), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false
                ]);
            }
        }

        // Set default status if not provided
        if (!$this->has('status')) {
            $this->merge(['status' => 'active']);
        }
    }
}
