<script lang="ts">
    import { onMount } from 'svelte';
    import { router } from '@inertiajs/svelte';
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Button } from '@/components/ui/button';
    import { Input } from '@/components/ui/input';
    import { Label } from '@/components/ui/label';
    import { Textarea } from '@/components/ui/textarea';
    import { Checkbox } from '@/components/ui/checkbox';
    import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select';
    import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
    import { api } from '@/lib/api';
    import type { LlmModel, CreateLlmModelRequest, BreadcrumbItem } from '@/types';
    import { Save, ArrowLeft, RefreshCw } from 'lucide-svelte';

    export let uuid: string;

    const breadcrumbItems: BreadcrumbItem[] = [
        {
            title: 'LLM Models',
            href: '/llm-models',
        },
        {
            title: 'Edit Model',
            href: `/llm-models/${uuid}/edit`,
        },
    ];

    let model: LlmModel | null = null;
    let form: CreateLlmModelRequest = {
        name: '',
        display_name: '',
        description: '',
        huggingface_id: '',
        huggingface_url: '',
        
        // Capabilities
        has_reasoning: false,
        has_tool_usage: false,
        has_vision: false,
        has_code_generation: false,
        has_function_calling: false,
        
        // Technical specifications
        context_window: undefined,
        parameter_count: '',
        architecture: '',
        quantization: '',
        format: '',
        
        // Chat and prompt formatting
        chat_template: '',
        
        // Performance and resource requirements
        model_size_bytes: undefined,
        estimated_vram_gb: undefined,
        benchmark_score: undefined,
        
        // Licensing and usage
        license: '',
        commercial_use: true,
        tags: [],
        
        status: 'active',
    };

    let loading = true;
    let saving = false;
    let errors: Record<string, string[]> = {};
    let tagInput = '';

    onMount(async () => {
        await loadModel();
    });

    async function loadModel() {
        loading = true;
        
        try {
            model = await api.getLlmModel(uuid);
            
            // Populate form with model data
            form = {
                name: model.name,
                display_name: model.display_name || '',
                description: model.description || '',
                huggingface_id: model.huggingface_id || '',
                huggingface_url: model.huggingface_url || '',
                
                // Capabilities
                has_reasoning: model.capabilities.reasoning,
                has_tool_usage: model.capabilities.tool_usage,
                has_vision: model.capabilities.vision,
                has_code_generation: model.capabilities.code_generation,
                has_function_calling: model.capabilities.function_calling,
                
                // Technical specifications
                context_window: model.specifications.context_window,
                parameter_count: model.specifications.parameter_count || '',
                architecture: model.specifications.architecture || '',
                quantization: model.specifications.quantization || '',
                format: model.specifications.format || '',
                
                // Chat and prompt formatting
                chat_template: model.formatting.chat_template || '',
                
                // Performance and resource requirements
                model_size_bytes: model.performance.model_size_bytes,
                estimated_vram_gb: model.performance.estimated_vram_gb,
                benchmark_score: model.performance.benchmark_score,
                
                // Licensing and usage
                license: model.licensing.license || '',
                commercial_use: model.licensing.commercial_use,
                tags: model.tags || [],
                
                status: model.status,
            };
        } catch (err) {
            console.error('Failed to load model:', err);
        } finally {
            loading = false;
        }
    }

    async function handleSubmit() {
        saving = true;
        errors = {};

        try {
            const updatedModel = await api.updateLlmModel(uuid, form);
            router.visit(`/llm-models/${updatedModel.uuid}`, {
                onSuccess: () => {
                    // Success handled by Inertia
                }
            });
        } catch (err: any) {
            if (err.response?.data?.errors) {
                errors = err.response.data.errors;
            } else {
                console.error('Failed to update model:', err);
            }
        } finally {
            saving = false;
        }
    }

    function addTag() {
        if (tagInput.trim() && !form.tags?.includes(tagInput.trim())) {
            form.tags = [...(form.tags || []), tagInput.trim()];
            tagInput = '';
        }
    }

    function removeTag(tag: string) {
        form.tags = form.tags?.filter(t => t !== tag) || [];
    }

    function handleTagKeydown(event: KeyboardEvent) {
        if (event.key === 'Enter') {
            event.preventDefault();
            addTag();
        }
    }
</script>

<svelte:head>
    <title>Edit {model?.display_name || model?.name || 'Model'}</title>
</svelte:head>

<AppLayout {breadcrumbItems}>
    {#if loading}
        <div class="flex items-center justify-center py-8">
            <RefreshCw class="h-6 w-6 animate-spin" />
            <span class="ml-2">Loading model...</span>
        </div>
    {:else if model}
        <div class="max-w-4xl mx-auto space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Edit Model</h1>
                    <p class="text-muted-foreground">
                        Update {model.display_name || model.name} details
                    </p>
                </div>
                <Button variant="outline" on:click={() => router.visit(`/llm-models/${uuid}`)}>
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    Back to Model
                </Button>
            </div>

            <form on:submit|preventDefault={handleSubmit} class="space-y-6">
                <!-- Basic Information -->
                <Card>
                    <CardHeader>
                        <CardTitle>Basic Information</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <Label for="name">Model Name *</Label>
                                <Input
                                    id="name"
                                    bind:value={form.name}
                                    placeholder="e.g., hf.co/unsloth/Qwen3-4B-GGUF:Q8_0"
                                    required
                                />
                                {#if errors.name}
                                    <p class="text-sm text-red-600">{errors.name[0]}</p>
                                {/if}
                            </div>

                            <div class="space-y-2">
                                <Label for="display_name">Display Name</Label>
                                <Input
                                    id="display_name"
                                    bind:value={form.display_name}
                                    placeholder="e.g., Qwen3 4B (Q8_0)"
                                />
                                {#if errors.display_name}
                                    <p class="text-sm text-red-600">{errors.display_name[0]}</p>
                                {/if}
                            </div>
                        </div>

                        <div class="space-y-2">
                            <Label for="description">Description</Label>
                            <Textarea
                                id="description"
                                bind:value={form.description}
                                placeholder="Describe the model's purpose and capabilities..."
                                rows={3}
                            />
                            {#if errors.description}
                                <p class="text-sm text-red-600">{errors.description[0]}</p>
                            {/if}
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <Label for="huggingface_id">Hugging Face ID</Label>
                                <Input
                                    id="huggingface_id"
                                    bind:value={form.huggingface_id}
                                    placeholder="e.g., unsloth/Qwen3-4B-GGUF"
                                />
                                {#if errors.huggingface_id}
                                    <p class="text-sm text-red-600">{errors.huggingface_id[0]}</p>
                                {/if}
                            </div>

                            <div class="space-y-2">
                                <Label for="status">Status</Label>
                                <Select bind:value={form.status}>
                                    <SelectTrigger>
                                        {form.status || "Select status"}
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="active">Active</SelectItem>
                                        <SelectItem value="inactive">Inactive</SelectItem>
                                        <SelectItem value="deprecated">Deprecated</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- Capabilities -->
                <Card>
                    <CardHeader>
                        <CardTitle>Capabilities</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex items-center space-x-2">
                                <Checkbox id="has_reasoning" bind:checked={form.has_reasoning} />
                                <Label for="has_reasoning">Reasoning</Label>
                            </div>

                            <div class="flex items-center space-x-2">
                                <Checkbox id="has_tool_usage" bind:checked={form.has_tool_usage} />
                                <Label for="has_tool_usage">Tool Usage</Label>
                            </div>

                            <div class="flex items-center space-x-2">
                                <Checkbox id="has_vision" bind:checked={form.has_vision} />
                                <Label for="has_vision">Vision</Label>
                            </div>

                            <div class="flex items-center space-x-2">
                                <Checkbox id="has_code_generation" bind:checked={form.has_code_generation} />
                                <Label for="has_code_generation">Code Generation</Label>
                            </div>

                            <div class="flex items-center space-x-2">
                                <Checkbox id="has_function_calling" bind:checked={form.has_function_calling} />
                                <Label for="has_function_calling">Function Calling</Label>
                            </div>

                            <div class="flex items-center space-x-2">
                                <Checkbox id="commercial_use" bind:checked={form.commercial_use} />
                                <Label for="commercial_use">Commercial Use</Label>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- Technical Specifications -->
                <Card>
                    <CardHeader>
                        <CardTitle>Technical Specifications</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div class="space-y-2">
                                <Label for="architecture">Architecture</Label>
                                <Input
                                    id="architecture"
                                    bind:value={form.architecture}
                                    placeholder="e.g., Qwen, Llama, Mistral"
                                />
                            </div>

                            <div class="space-y-2">
                                <Label for="parameter_count">Parameter Count</Label>
                                <Input
                                    id="parameter_count"
                                    bind:value={form.parameter_count}
                                    placeholder="e.g., 4B, 7B, 70B"
                                />
                            </div>

                            <div class="space-y-2">
                                <Label for="quantization">Quantization</Label>
                                <Input
                                    id="quantization"
                                    bind:value={form.quantization}
                                    placeholder="e.g., Q8_0, Q4_K_M, FP16"
                                />
                            </div>

                            <div class="space-y-2">
                                <Label for="format">Format</Label>
                                <Input
                                    id="format"
                                    bind:value={form.format}
                                    placeholder="e.g., GGUF, SafeTensors"
                                />
                            </div>

                            <div class="space-y-2">
                                <Label for="context_window">Context Window</Label>
                                <Input
                                    id="context_window"
                                    type="number"
                                    bind:value={form.context_window}
                                    placeholder="e.g., 4096, 8192"
                                />
                            </div>

                            <div class="space-y-2">
                                <Label for="estimated_vram_gb">Estimated VRAM (GB)</Label>
                                <Input
                                    id="estimated_vram_gb"
                                    type="number"
                                    bind:value={form.estimated_vram_gb}
                                    placeholder="e.g., 8, 16, 24"
                                />
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <Label for="license">License</Label>
                                <Input
                                    id="license"
                                    bind:value={form.license}
                                    placeholder="e.g., MIT, Apache-2.0, Custom"
                                />
                            </div>

                            <div class="space-y-2">
                                <Label for="chat_template">Chat Template</Label>
                                <Input
                                    id="chat_template"
                                    bind:value={form.chat_template}
                                    placeholder="e.g., ChatML, Alpaca, Custom"
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- Tags -->
                <Card>
                    <CardHeader>
                        <CardTitle>Tags</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="flex gap-2">
                            <Input
                                bind:value={tagInput}
                                placeholder="Add a tag..."
                                on:keydown={handleTagKeydown}
                            />
                            <Button type="button" variant="outline" on:click={addTag}>
                                Add
                            </Button>
                        </div>

                        {#if form.tags && form.tags.length > 0}
                            <div class="flex flex-wrap gap-2">
                                {#each form.tags as tag}
                                    <span class="inline-flex items-center gap-1 px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-sm">
                                        {tag}
                                        <button
                                            type="button"
                                            on:click={() => removeTag(tag)}
                                            class="ml-1 text-muted-foreground hover:text-foreground"
                                        >
                                            ×
                                        </button>
                                    </span>
                                {/each}
                            </div>
                        {/if}
                    </CardContent>
                </Card>

                <!-- Actions -->
                <div class="flex justify-end gap-4">
                    <Button
                        type="button"
                        variant="outline"
                        on:click={() => router.visit(`/llm-models/${uuid}`)}
                    >
                        Cancel
                    </Button>
                    <Button type="submit" disabled={saving}>
                        {#if saving}
                            Saving...
                        {:else}
                            <Save class="mr-2 h-4 w-4" />
                            Save Changes
                        {/if}
                    </Button>
                </div>
            </form>
        </div>
    {/if}
</AppLayout>
